
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 217.2 32.6% 17.5%;
    --card-foreground: 210 40% 98%;

    --popover: 217.2 32.6% 17.5%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 45 100% 70%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;

    --radius: 0.5rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-arabic;
    direction: rtl;
  }

  /* خط عربي مخصص */
  .font-arabic {
    font-family: 'Noto Sans Arabic', 'Cairo', sans-serif;
  }

  /* تحسينات للعملة */
  .currency {
    font-weight: 600;
    color: hsl(var(--accent));
  }
}

/* استيراد خط عربي */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
