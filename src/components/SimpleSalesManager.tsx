import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ShoppingCart, Plus } from 'lucide-react';

interface Sale {
  id: string;
  customerName: string;
  items: string;
  total: number;
  date: Date;
  status: string;
}

const SimpleSalesManager = () => {
  const [sales, setSales] = useState<Sale[]>([
    {
      id: '1',
      customerName: 'أحمد محمد',
      items: 'صابون أومو × 2',
      total: 51.00,
      date: new Date('2024-01-20'),
      status: 'مكتملة'
    },
    {
      id: '2',
      customerName: 'فاطمة علي',
      items: 'فيري منظف أطباق × 1',
      total: 15.75,
      date: new Date('2024-01-19'),
      status: 'مكتملة'
    }
  ]);

  const [showNewSale, setShowNewSale] = useState(false);
  const [newSale, setNewSale] = useState({
    customerName: '',
    items: '',
    total: ''
  });

  const handleAddSale = () => {
    if (!newSale.customerName || !newSale.items || !newSale.total) {
      alert('يرجى ملء جميع الحقول');
      return;
    }

    const sale: Sale = {
      id: Date.now().toString(),
      customerName: newSale.customerName,
      items: newSale.items,
      total: parseFloat(newSale.total),
      date: new Date(),
      status: 'مكتملة'
    };

    setSales([sale, ...sales]);
    setNewSale({
      customerName: '',
      items: '',
      total: ''
    });
    setShowNewSale(false);
    alert('تم إضافة الفاتورة بنجاح');
  };

  return (
    <div className="space-y-6 p-4">
      {/* Header */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <ShoppingCart className="w-5 h-5" />
            إدارة المبيعات
          </CardTitle>
          <CardDescription className="text-blue-200">
            إنشاء فواتير جديدة وإدارة المبيعات
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={() => setShowNewSale(!showNewSale)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="w-4 h-4 ml-2" />
            {showNewSale ? 'إلغاء' : 'فاتورة جديدة'}
          </Button>
        </CardContent>
      </Card>

      {/* New Sale Form */}
      {showNewSale && (
        <Card className="border-green-600/50 bg-slate-800/50">
          <CardHeader>
            <CardTitle className="text-white">إنشاء فاتورة جديدة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-white">اسم العميل</Label>
              <Input
                value={newSale.customerName}
                onChange={(e) => setNewSale({...newSale, customerName: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="اسم العميل"
              />
            </div>
            
            <div>
              <Label className="text-white">المنتجات</Label>
              <Input
                value={newSale.items}
                onChange={(e) => setNewSale({...newSale, items: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="مثال: صابون أومو × 2"
              />
            </div>

            <div>
              <Label className="text-white">المبلغ الإجمالي</Label>
              <Input
                type="number"
                value={newSale.total}
                onChange={(e) => setNewSale({...newSale, total: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="0.00"
              />
            </div>

            <Button 
              onClick={handleAddSale}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              إنشاء الفاتورة
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Sales List */}
      <Card className="border-slate-600 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white">سجل المبيعات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sales.map(sale => (
              <Card key={sale.id} className="border-slate-700 bg-slate-700/50">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-white font-semibold">فاتورة #{sale.id.slice(-6)}</h3>
                      <p className="text-gray-300">العميل: {sale.customerName}</p>
                      <p className="text-gray-300">المنتجات: {sale.items}</p>
                      <p className="text-gray-400 text-sm">
                        {sale.date.toLocaleDateString('ar-EG')}
                      </p>
                    </div>
                    <div className="text-left">
                      <p className="text-white font-bold text-lg">
                        {sale.total.toFixed(2)} ج.م
                      </p>
                      <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">
                        {sale.status}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SimpleSalesManager;
