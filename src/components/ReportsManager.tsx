
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { FileText, TrendingUp, TrendingDown, Users, Package, CreditCard, Calendar, Calculator } from 'lucide-react';

// Mock data for reports
const mockSalesData = [
  { id: '1', date: new Date(), customerName: 'أحمد محمد', total: 125.50, profit: 45.75, items: 3 },
  { id: '2', date: new Date(Date.now() - 86400000), customerName: 'فاطمة علي', total: 89.25, profit: 32.10, items: 2 },
  { id: '3', date: new Date(Date.now() - 172800000), customerName: 'محمد حسن', total: 210.00, profit: 78.50, items: 5 },
  { id: '4', date: new Date(Date.now() - 259200000), customerName: 'عائشة أحمد', total: 67.75, profit: 23.25, items: 2 },
  { id: '5', date: new Date(Date.now() - 345600000), customerName: 'يوسف محمود', total: 156.80, profit: 58.90, items: 4 },
];

const mockProductSales = [
  { name: 'صابون أومو', quantity: 25, revenue: 637.50, profit: 237.50 },
  { name: 'فيري منظف أطباق', quantity: 18, revenue: 283.50, profit: 108.00 },
  { name: 'كلوركس مطهر', quantity: 15, revenue: 180.00, profit: 67.50 },
  { name: 'داوني معطر ملابس', quantity: 12, revenue: 219.00, profit: 87.60 },
  { name: 'تايد مسحوق غسيل', quantity: 8, revenue: 168.00, profit: 64.00 },
];

const ReportsManager = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('daily');
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth().toString());
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());

  const calculateTotals = () => {
    const totalSales = mockSalesData.reduce((sum, sale) => sum + sale.total, 0);
    const totalProfit = mockSalesData.reduce((sum, sale) => sum + sale.profit, 0);
    const totalTransactions = mockSalesData.length;
    const averageTransaction = totalSales / totalTransactions || 0;
    
    return { totalSales, totalProfit, totalTransactions, averageTransaction };
  };

  const { totalSales, totalProfit, totalTransactions, averageTransaction } = calculateTotals();

  const months = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i);

  const getPeriodTitle = () => {
    switch (selectedPeriod) {
      case 'daily':
        return 'تقرير اليوم';
      case 'weekly':
        return 'تقرير الأسبوع';
      case 'monthly':
        return `تقرير ${months[parseInt(selectedMonth)]} ${selectedYear}`;
      default:
        return 'التقرير';
    }
  };

  // Debug logging removed for cleaner output

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <FileText className="w-5 h-5" />
            التقارير والإحصائيات
          </CardTitle>
          <CardDescription className="text-blue-200">
            عرض تفصيلي للمبيعات والأرباح والمنتجات الأكثر مبيعاً
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="اختر الفترة الزمنية" />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="daily">تقرير يومي</SelectItem>
                  <SelectItem value="weekly">تقرير أسبوعي</SelectItem>
                  <SelectItem value="monthly">تقرير شهري</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {selectedPeriod === 'monthly' && (
              <>
                <div className="flex-1">
                  <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      {months.map((month, index) => (
                        <SelectItem key={index} value={index.toString()}>{month}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <Select value={selectedYear} onValueChange={setSelectedYear}>
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      {years.map(year => (
                        <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-blue-600/30 bg-gradient-to-br from-blue-600/10 to-blue-800/10">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-600/20 rounded-lg mx-auto mb-4">
              <TrendingUp className="w-6 h-6 text-blue-400" />
            </div>
            <p className="text-2xl font-bold text-white currency">{totalSales.toFixed(2)} ج.م</p>
            <p className="text-blue-200 text-sm">إجمالي المبيعات</p>
          </CardContent>
        </Card>

        <Card className="border-green-600/30 bg-gradient-to-br from-green-600/10 to-green-800/10">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-green-600/20 rounded-lg mx-auto mb-4">
              <TrendingUp className="w-6 h-6 text-green-400" />
            </div>
            <p className="text-2xl font-bold text-white currency">{totalProfit.toFixed(2)} ج.م</p>
            <p className="text-green-200 text-sm">صافي الربح</p>
          </CardContent>
        </Card>

        <Card className="border-purple-600/30 bg-gradient-to-br from-purple-600/10 to-purple-800/10">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-600/20 rounded-lg mx-auto mb-4">
              <Users className="w-6 h-6 text-purple-400" />
            </div>
            <p className="text-2xl font-bold text-white">{totalTransactions}</p>
            <p className="text-purple-200 text-sm">عدد المعاملات</p>
          </CardContent>
        </Card>

        <Card className="border-orange-600/30 bg-gradient-to-br from-orange-600/10 to-orange-800/10">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-orange-600/20 rounded-lg mx-auto mb-4">
              <Calculator className="w-6 h-6 text-orange-400" />
            </div>
            <p className="text-2xl font-bold text-white currency">{averageTransaction.toFixed(2)} ج.م</p>
            <p className="text-orange-200 text-sm">متوسط المعاملة</p>
          </CardContent>
        </Card>
      </div>

      {/* Sales Details */}
      <Card className="border-slate-600 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            {getPeriodTitle()} - تفاصيل المبيعات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockSalesData.map(sale => (
              <Card key={sale.id} className="border-slate-700 bg-slate-700/50">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-white font-semibold">{sale.customerName}</h3>
                        <Badge className="bg-blue-600/20 text-blue-300">
                          {sale.items} منتج
                        </Badge>
                      </div>
                      <p className="text-gray-400 text-sm">
                        {sale.date.toLocaleDateString('ar-EG')} - {sale.date.toLocaleTimeString('ar-EG')}
                      </p>
                    </div>
                    <div className="text-left">
                      <p className="text-white font-bold text-lg currency">{sale.total.toFixed(2)} ج.م</p>
                      <p className="text-green-400 text-sm">
                        ربح: {sale.profit.toFixed(2)} ج.م
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Products */}
      <Card className="border-slate-600 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Package className="w-5 h-5" />
            المنتجات الأكثر مبيعاً
          </CardTitle>
          <CardDescription className="text-blue-200">
            ترتيب المنتجات حسب الكمية المباعة
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockProductSales.map((product, index) => (
              <Card key={product.name} className="border-slate-700 bg-slate-700/50">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full text-white font-bold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <h3 className="text-white font-semibold">{product.name}</h3>
                        <p className="text-gray-400 text-sm">
                          {product.quantity} وحدة مباعة
                        </p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="text-white font-bold currency">{product.revenue.toFixed(2)} ج.م</p>
                      <p className="text-green-400 text-sm">
                        ربح: {product.profit.toFixed(2)} ج.م
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Debts Summary */}
      <Card className="border-yellow-600/50 bg-yellow-600/5">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            ملخص المديونيات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <p className="text-2xl font-bold text-red-400 currency">1,250.75 ج.م</p>
              <p className="text-gray-300 text-sm">إجمالي الديون المعلقة</p>
            </div>
            <div className="text-center p-4">
              <p className="text-2xl font-bold text-yellow-400">8</p>
              <p className="text-gray-300 text-sm">عدد العملاء المدينين</p>
            </div>
            <div className="text-center p-4">
              <p className="text-2xl font-bold text-green-400 currency">890.25 ج.م</p>
              <p className="text-gray-300 text-sm">إجمالي المبالغ المحصلة</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsManager;
