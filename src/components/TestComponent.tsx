import React from 'react';

const TestComponent = () => {
  return (
    <div style={{
      padding: '2rem',
      backgroundColor: 'white',
      color: 'black',
      minHeight: '400px',
      border: '2px solid #3b82f6',
      borderRadius: '8px',
      margin: '1rem'
    }}>
      <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '1rem' }}>
        ✅ مكون اختبار يعمل!
      </h1>
      <p style={{ fontSize: '1.1rem', marginBottom: '1rem' }}>
        هذا مكون اختبار للتأكد من أن التبويبات تعمل بشكل صحيح.
      </p>
      <div style={{
        padding: '1rem',
        backgroundColor: '#dbeafe',
        border: '1px solid #3b82f6',
        borderRadius: '4px'
      }}>
        <p style={{ color: '#1e40af' }}>
          🎉 إذا كنت ترى هذا النص، فإن التبويب يعمل بشكل صحيح!
        </p>
        <p style={{ fontSize: '0.9rem', color: '#6b7280', marginTop: '0.5rem' }}>
          الوقت الحالي: {new Date().toLocaleString('ar-EG')}
        </p>
      </div>
    </div>
  );
};

export default TestComponent;
