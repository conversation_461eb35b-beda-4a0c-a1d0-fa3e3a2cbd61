
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Settings, Database, Users, FileText, Download, Upload } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const SettingsManager = () => {
  const [settings, setSettings] = useState({
    storeName: 'محل المنظفات الذهبي',
    storeAddress: 'شارع النصر، المنصورة، مصر',
    storePhone: '01234567890',
    defaultTax: '14',
    defaultDiscount: '0',
    currency: 'EGP',
    autoBackup: true,
    printReceipts: true,
    lowStockAlert: true,
    lowStockThreshold: '5'
  });

  // تحميل الإعدادات من localStorage عند بدء التشغيل
  React.useEffect(() => {
    const savedSettings = localStorage.getItem('storeSettings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsedSettings }));
      } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
      }
    }
  }, []);

  const [newUser, setNewUser] = useState({
    name: '',
    username: '',
    password: '',
    role: 'employee'
  });

  const { toast } = useToast();

  const handleSettingChange = (key: string, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = () => {
    // في التطبيق الحقيقي، ستحفظ في قاعدة البيانات
    localStorage.setItem('storeSettings', JSON.stringify(settings));
    toast({
      title: "تم بنجاح",
      description: "تم حفظ الإعدادات بنجاح",
    });
  };

  const exportData = () => {
    // في التطبيق الحقيقي، ستصدر البيانات الفعلية
    const data = {
      exportDate: new Date(),
      settings: settings,
      note: 'نسخة احتياطية من بيانات المحل'
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    
    toast({
      title: "تم بنجاح",
      description: "تم تصدير النسخة الاحتياطية بنجاح",
    });
  };

  const importData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        if (data.settings) {
          setSettings(data.settings);
          toast({
            title: "تم بنجاح",
            description: "تم استيراد البيانات بنجاح",
          });
        }
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في قراءة الملف",
          variant: "destructive"
        });
      }
    };
    reader.readAsText(file);
  };

  const addUser = () => {
    if (!newUser.name || !newUser.username || !newUser.password) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول",
        variant: "destructive"
      });
      return;
    }

    // في التطبيق الحقيقي، ستضيف المستخدم لقاعدة البيانات
    toast({
      title: "تم بنجاح",
      description: `تم إضافة المستخدم ${newUser.name} بنجاح`,
    });
    
    setNewUser({ name: '', username: '', password: '', role: 'employee' });
  };

  return (
    <div className="space-y-6 min-h-screen">
      {/* Header */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Settings className="w-5 h-5" />
            إعدادات النظام
          </CardTitle>
          <CardDescription className="text-blue-200">
            تخصيص النظام وإدارة المستخدمين والنسخ الاحتياطية
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Main Settings */}
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-slate-800">
          <TabsTrigger value="general">الإعدادات العامة</TabsTrigger>
          <TabsTrigger value="users">المستخدمين</TabsTrigger>
          <TabsTrigger value="backup">النسخ الاحتياطية</TabsTrigger>
          <TabsTrigger value="preferences">التفضيلات</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card className="border-slate-600 bg-slate-800/50">
            <CardHeader>
              <CardTitle className="text-white">معلومات المحل</CardTitle>
              <CardDescription className="text-blue-200">
                البيانات الأساسية للمحل
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="store-name" className="text-white">اسم المحل</Label>
                <Input
                  id="store-name"
                  value={settings.storeName}
                  onChange={(e) => handleSettingChange('storeName', e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
              <div>
                <Label htmlFor="store-address" className="text-white">عنوان المحل</Label>
                <Input
                  id="store-address"
                  value={settings.storeAddress}
                  onChange={(e) => handleSettingChange('storeAddress', e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
              <div>
                <Label htmlFor="store-phone" className="text-white">رقم الهاتف</Label>
                <Input
                  id="store-phone"
                  value={settings.storePhone}
                  onChange={(e) => handleSettingChange('storePhone', e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="default-tax" className="text-white">نسبة الضريبة الافتراضية (%)</Label>
                  <Input
                    id="default-tax"
                    type="number"
                    value={settings.defaultTax}
                    onChange={(e) => handleSettingChange('defaultTax', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <Label htmlFor="default-discount" className="text-white">نسبة الخصم الافتراضية (%)</Label>
                  <Input
                    id="default-discount"
                    type="number"
                    value={settings.defaultDiscount}
                    onChange={(e) => handleSettingChange('defaultDiscount', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>

              <Button onClick={saveSettings} className="bg-blue-600 hover:bg-blue-700">
                حفظ الإعدادات
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card className="border-slate-600 bg-slate-800/50">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Users className="w-5 h-5" />
                إدارة المستخدمين
              </CardTitle>
              <CardDescription className="text-blue-200">
                إضافة مستخدمين جدد وإدارة الصلاحيات
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <h3 className="text-white font-semibold">إضافة مستخدم جديد</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="user-name" className="text-white">اسم المستخدم</Label>
                    <Input
                      id="user-name"
                      value={newUser.name}
                      onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="أحمد محمد"
                    />
                  </div>
                  <div>
                    <Label htmlFor="username" className="text-white">اسم المستخدم للدخول</Label>
                    <Input
                      id="username"
                      value={newUser.username}
                      onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="ahmed123"
                    />
                  </div>
                  <div>
                    <Label htmlFor="user-password" className="text-white">كلمة المرور</Label>
                    <Input
                      id="user-password"
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                  </div>
                  <div>
                    <Label htmlFor="user-role" className="text-white">نوع المستخدم</Label>
                    <select
                      id="user-role"
                      value={newUser.role}
                      onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                      className="w-full p-2 bg-slate-700 border border-slate-600 rounded-md text-white"
                    >
                      <option value="employee">موظف</option>
                      <option value="manager">مدير</option>
                      <option value="admin">مدير عام</option>
                    </select>
                  </div>
                </div>
                <Button onClick={addUser} className="bg-green-600 hover:bg-green-700">
                  إضافة المستخدم
                </Button>
              </div>

              <div className="mt-8">
                <h3 className="text-white font-semibold mb-4">المستخدمين الحاليين</h3>
                <div className="space-y-2">
                  <Card className="border-slate-700 bg-slate-700/50">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-white font-medium">المدير العام</p>
                          <p className="text-gray-300 text-sm">admin</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">مدير عام</span>
                          <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">نشط</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="backup" className="space-y-4">
          <Card className="border-slate-600 bg-slate-800/50">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Database className="w-5 h-5" />
                النسخ الاحتياطية
              </CardTitle>
              <CardDescription className="text-blue-200">
                تصدير واستيراد البيانات للحفاظ على أمان المعلومات
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-white font-semibold mb-4">تصدير البيانات</h3>
                <p className="text-gray-300 mb-4">
                  قم بتصدير جميع بيانات المحل في ملف احتياطي
                </p>
                <Button onClick={exportData} className="bg-blue-600 hover:bg-blue-700">
                  <Download className="w-4 h-4 ml-2" />
                  تصدير نسخة احتياطية
                </Button>
              </div>

              <div>
                <h3 className="text-white font-semibold mb-4">استيراد البيانات</h3>
                <p className="text-gray-300 mb-4">
                  استيراد البيانات من ملف نسخة احتياطية سابقة
                </p>
                <div className="flex items-center gap-4">
                  <input
                    type="file"
                    accept=".json"
                    onChange={importData}
                    className="hidden"
                    id="import-file"
                  />
                  <Button 
                    onClick={() => document.getElementById('import-file')?.click()}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Upload className="w-4 h-4 ml-2" />
                    استيراد نسخة احتياطية
                  </Button>
                </div>
              </div>

              <div className="bg-yellow-600/10 border border-yellow-600/30 rounded-lg p-4">
                <h4 className="text-yellow-400 font-semibold mb-2">تنبيه مهم</h4>
                <p className="text-yellow-200 text-sm">
                  يُنصح بعمل نسخة احتياطية بشكل دوري لحماية البيانات من الفقدان. 
                  احتفظ بالنسخ الاحتياطية في مكان آمن.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences" className="space-y-4">
          <Card className="border-slate-600 bg-slate-800/50">
            <CardHeader>
              <CardTitle className="text-white">تفضيلات النظام</CardTitle>
              <CardDescription className="text-blue-200">
                تخصيص سلوك النظام والتنبيهات
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-white">النسخ الاحتياطي التلقائي</Label>
                  <p className="text-gray-400 text-sm">عمل نسخة احتياطية تلقائياً كل يوم</p>
                </div>
                <Switch
                  checked={settings.autoBackup}
                  onCheckedChange={(checked) => handleSettingChange('autoBackup', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-white">طباعة الفواتير تلقائياً</Label>
                  <p className="text-gray-400 text-sm">طباعة الفاتورة فور إنشائها</p>
                </div>
                <Switch
                  checked={settings.printReceipts}
                  onCheckedChange={(checked) => handleSettingChange('printReceipts', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-white">تنبيه نفاد المخزون</Label>
                  <p className="text-gray-400 text-sm">تنبيه عند انخفاض كمية المنتج</p>
                </div>
                <Switch
                  checked={settings.lowStockAlert}
                  onCheckedChange={(checked) => handleSettingChange('lowStockAlert', checked)}
                />
              </div>

              {settings.lowStockAlert && (
                <div>
                  <Label htmlFor="stock-threshold" className="text-white">حد التنبيه للمخزون</Label>
                  <Input
                    id="stock-threshold"
                    type="number"
                    value={settings.lowStockThreshold}
                    onChange={(e) => handleSettingChange('lowStockThreshold', e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white w-32"
                  />
                  <p className="text-gray-400 text-sm mt-1">
                    سيظهر تنبيه عندما تصل الكمية لهذا الرقم أو أقل
                  </p>
                </div>
              )}

              <Button onClick={saveSettings} className="bg-blue-600 hover:bg-blue-700">
                حفظ التفضيلات
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SettingsManager;
