import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Users, Plus, Phone, MapPin } from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  phone: string;
  address: string;
  totalDebt: number;
}

const SimpleCustomersManager = () => {
  const [customers, setCustomers] = useState<Customer[]>([
    {
      id: '1',
      name: 'أحمد محمد',
      phone: '01234567890',
      address: 'شارع النصر، المنصورة',
      totalDebt: 250.00
    },
    {
      id: '2',
      name: 'فاطمة علي',
      phone: '01098765432',
      address: 'شارع الجمهورية، القاهرة',
      totalDebt: 0
    }
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    phone: '',
    address: ''
  });

  const handleAddCustomer = () => {
    if (!newCustomer.name || !newCustomer.phone) {
      alert('يرجى ملء الاسم ورقم الهاتف على الأقل');
      return;
    }

    const customer: Customer = {
      id: Date.now().toString(),
      name: newCustomer.name,
      phone: newCustomer.phone,
      address: newCustomer.address,
      totalDebt: 0
    };

    setCustomers([...customers, customer]);
    setNewCustomer({
      name: '',
      phone: '',
      address: ''
    });
    setShowAddForm(false);
    alert('تم إضافة العميل بنجاح');
  };

  return (
    <div className="space-y-6 p-4">
      {/* Header */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Users className="w-5 h-5" />
            إدارة العملاء
          </CardTitle>
          <CardDescription className="text-blue-200">
            إدارة بيانات العملاء ومتابعة المديونيات
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <Card className="border-blue-600/30 bg-blue-600/10">
              <CardContent className="p-4 text-center">
                <Users className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">{customers.length}</p>
                <p className="text-blue-200 text-sm">إجمالي العملاء</p>
              </CardContent>
            </Card>
            <Card className="border-yellow-600/30 bg-yellow-600/10">
              <CardContent className="p-4 text-center">
                <Users className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">
                  {customers.filter(c => c.totalDebt > 0).length}
                </p>
                <p className="text-yellow-200 text-sm">عملاء لديهم ديون</p>
              </CardContent>
            </Card>
            <Card className="border-red-600/30 bg-red-600/10">
              <CardContent className="p-4 text-center">
                <Users className="w-8 h-8 text-red-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">
                  {customers.reduce((sum, c) => sum + c.totalDebt, 0).toFixed(2)} ج.م
                </p>
                <p className="text-red-200 text-sm">إجمالي الديون</p>
              </CardContent>
            </Card>
          </div>
          
          <Button 
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="w-4 h-4 ml-2" />
            {showAddForm ? 'إلغاء' : 'إضافة عميل جديد'}
          </Button>
        </CardContent>
      </Card>

      {/* Add Customer Form */}
      {showAddForm && (
        <Card className="border-green-600/50 bg-slate-800/50">
          <CardHeader>
            <CardTitle className="text-white">إضافة عميل جديد</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-white">اسم العميل *</Label>
              <Input
                value={newCustomer.name}
                onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="أحمد محمد"
              />
            </div>
            
            <div>
              <Label className="text-white">رقم الهاتف *</Label>
              <Input
                value={newCustomer.phone}
                onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="01234567890"
              />
            </div>

            <div>
              <Label className="text-white">العنوان (اختياري)</Label>
              <Input
                value={newCustomer.address}
                onChange={(e) => setNewCustomer({...newCustomer, address: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="شارع النصر، المنصورة"
              />
            </div>

            <Button 
              onClick={handleAddCustomer}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              إضافة العميل
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Customers List */}
      <div className="grid gap-4">
        {customers.map(customer => (
          <Card key={customer.id} className="border-slate-600 bg-slate-800/50">
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white mb-2">{customer.name}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-blue-400" />
                      <span className="text-white">{customer.phone}</span>
                    </div>
                    {customer.address && (
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-blue-400" />
                        <span className="text-gray-300">{customer.address}</span>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-400">إجمالي الديون:</span>
                      <span className={`font-medium ml-2 ${customer.totalDebt > 0 ? 'text-red-400' : 'text-green-400'}`}>
                        {customer.totalDebt.toFixed(2)} ج.م
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default SimpleCustomersManager;
