import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, TrendingUp, Users, Package, CreditCard } from 'lucide-react';

const SimpleReportsManager = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('daily');

  const salesData = [
    { id: '1', date: new Date(), customerName: 'أحمد محمد', total: 125.50, profit: 45.75 },
    { id: '2', date: new Date(Date.now() - 86400000), customerName: 'فاطمة علي', total: 89.25, profit: 32.10 },
    { id: '3', date: new Date(Date.now() - 172800000), customerName: 'محمد حسن', total: 210.00, profit: 78.50 }
  ];

  const productSales = [
    { name: 'صابون أومو', quantity: 25, revenue: 637.50 },
    { name: 'فيري منظف أطباق', quantity: 18, revenue: 283.50 },
    { name: 'كلوركس مطهر', quantity: 15, revenue: 180.00 }
  ];

  const totalSales = salesData.reduce((sum, sale) => sum + sale.total, 0);
  const totalProfit = salesData.reduce((sum, sale) => sum + sale.profit, 0);
  const totalTransactions = salesData.length;

  return (
    <div className="space-y-6 p-4">
      {/* Header */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <FileText className="w-5 h-5" />
            التقارير والإحصائيات
          </CardTitle>
          <CardDescription className="text-blue-200">
            عرض تفصيلي للمبيعات والأرباح والمنتجات الأكثر مبيعاً
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="p-2 bg-slate-700 border border-slate-600 rounded-md text-white"
            >
              <option value="daily">تقرير يومي</option>
              <option value="weekly">تقرير أسبوعي</option>
              <option value="monthly">تقرير شهري</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-blue-600/30 bg-gradient-to-br from-blue-600/10 to-blue-800/10">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-600/20 rounded-lg mx-auto mb-4">
              <TrendingUp className="w-6 h-6 text-blue-400" />
            </div>
            <p className="text-2xl font-bold text-white">{totalSales.toFixed(2)} ج.م</p>
            <p className="text-blue-200 text-sm">إجمالي المبيعات</p>
          </CardContent>
        </Card>

        <Card className="border-green-600/30 bg-gradient-to-br from-green-600/10 to-green-800/10">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-green-600/20 rounded-lg mx-auto mb-4">
              <TrendingUp className="w-6 h-6 text-green-400" />
            </div>
            <p className="text-2xl font-bold text-white">{totalProfit.toFixed(2)} ج.م</p>
            <p className="text-green-200 text-sm">صافي الربح</p>
          </CardContent>
        </Card>

        <Card className="border-purple-600/30 bg-gradient-to-br from-purple-600/10 to-purple-800/10">
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-600/20 rounded-lg mx-auto mb-4">
              <Users className="w-6 h-6 text-purple-400" />
            </div>
            <p className="text-2xl font-bold text-white">{totalTransactions}</p>
            <p className="text-purple-200 text-sm">عدد المعاملات</p>
          </CardContent>
        </Card>
      </div>

      {/* Sales Details */}
      <Card className="border-slate-600 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white">تفاصيل المبيعات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {salesData.map(sale => (
              <Card key={sale.id} className="border-slate-700 bg-slate-700/50">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="text-white font-semibold">{sale.customerName}</h3>
                      <p className="text-gray-400 text-sm">
                        {sale.date.toLocaleDateString('ar-EG')}
                      </p>
                    </div>
                    <div className="text-left">
                      <p className="text-white font-bold text-lg">{sale.total.toFixed(2)} ج.م</p>
                      <p className="text-green-400 text-sm">
                        ربح: {sale.profit.toFixed(2)} ج.م
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Products */}
      <Card className="border-slate-600 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Package className="w-5 h-5" />
            المنتجات الأكثر مبيعاً
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {productSales.map((product, index) => (
              <Card key={product.name} className="border-slate-700 bg-slate-700/50">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full text-white font-bold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <h3 className="text-white font-semibold">{product.name}</h3>
                        <p className="text-gray-400 text-sm">
                          {product.quantity} وحدة مباعة
                        </p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="text-white font-bold">{product.revenue.toFixed(2)} ج.م</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Debts Summary */}
      <Card className="border-yellow-600/50 bg-yellow-600/5">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            ملخص المديونيات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <p className="text-2xl font-bold text-red-400">1,250.75 ج.م</p>
              <p className="text-gray-300 text-sm">إجمالي الديون المعلقة</p>
            </div>
            <div className="text-center p-4">
              <p className="text-2xl font-bold text-yellow-400">8</p>
              <p className="text-gray-300 text-sm">عدد العملاء المدينين</p>
            </div>
            <div className="text-center p-4">
              <p className="text-2xl font-bold text-green-400">890.25 ج.م</p>
              <p className="text-gray-300 text-sm">إجمالي المبالغ المحصلة</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SimpleReportsManager;
