import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ShoppingCart, Plus, Trash2, Calculator } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SaleItem {
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface Sale {
  id: string;
  customerId?: string;
  customerName?: string;
  items: SaleItem[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  amountPaid: number;
  remainingAmount: number;
  createdAt: Date;
  status: 'completed' | 'partial' | 'pending';
}

// Mock data for products (في التطبيق الحقيقي ستأتي من إدارة المنتجات)
const mockProducts = [
  { id: '1', name: 'صابون أومو', sellPrice: 25.50, quantity: 100 },
  { id: '2', name: 'فيري منظف أطباق', sellPrice: 15.75, quantity: 50 },
  { id: '3', name: 'كلوركس مطهر', sellPrice: 12.00, quantity: 75 },
  { id: '4', name: 'داوني معطر ملابس', sellPrice: 18.25, quantity: 30 },
];

const SalesManager = () => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [isCreatingSale, setIsCreatingSale] = useState(false);
  const [currentSale, setCurrentSale] = useState<SaleItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState('');
  const [quantity, setQuantity] = useState('1');
  const [customerName, setCustomerName] = useState('');
  const [discount, setDiscount] = useState('0');
  const [tax, setTax] = useState('0');
  const [amountPaid, setAmountPaid] = useState('');
  const { toast } = useToast();

  // Debug logging
  console.log('SalesManager - selectedProduct:', selectedProduct);
  console.log('SalesManager - mockProducts:', mockProducts);

  const addProductToSale = () => {
    const product = mockProducts.find(p => p.id === selectedProduct);
    if (!product || !quantity) {
      toast({
        title: "خطأ",
        description: "يرجى اختيار منتج وإدخال الكمية",
        variant: "destructive"
      });
      return;
    }

    const qty = parseInt(quantity);
    if (qty > product.quantity) {
      toast({
        title: "خطأ",
        description: "الكمية المطلوبة أكبر من المتوفر في المخزون",
        variant: "destructive"
      });
      return;
    }

    const existingItemIndex = currentSale.findIndex(item => item.productId === selectedProduct);
    
    if (existingItemIndex >= 0) {
      const updatedSale = [...currentSale];
      updatedSale[existingItemIndex].quantity += qty;
      updatedSale[existingItemIndex].total = updatedSale[existingItemIndex].quantity * product.sellPrice;
      setCurrentSale(updatedSale);
    } else {
      const newItem: SaleItem = {
        productId: selectedProduct,
        productName: product.name,
        quantity: qty,
        unitPrice: product.sellPrice,
        total: qty * product.sellPrice
      };
      setCurrentSale([...currentSale, newItem]);
    }

    setSelectedProduct('');
    setQuantity('1');
    
    toast({
      title: "تم بنجاح",
      description: "تم إضافة المنتج للفاتورة",
    });
  };

  const removeProductFromSale = (productId: string) => {
    setCurrentSale(currentSale.filter(item => item.productId !== productId));
  };

  const calculateSubtotal = () => {
    return currentSale.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const discountAmount = (subtotal * parseFloat(discount || '0')) / 100;
    const taxAmount = ((subtotal - discountAmount) * parseFloat(tax || '0')) / 100;
    return subtotal - discountAmount + taxAmount;
  };

  const completeSale = () => {
    if (currentSale.length === 0) {
      toast({
        title: "خطأ",
        description: "يرجى إضافة منتجات للفاتورة",
        variant: "destructive"
      });
      return;
    }

    const subtotal = calculateSubtotal();
    const total = calculateTotal();
    const paid = parseFloat(amountPaid || '0');
    const remaining = total - paid;

    const newSale: Sale = {
      id: Date.now().toString(),
      customerName: customerName || 'عميل غير مسجل',
      items: [...currentSale],
      subtotal,
      discount: parseFloat(discount || '0'),
      tax: parseFloat(tax || '0'),
      total,
      amountPaid: paid,
      remainingAmount: remaining,
      createdAt: new Date(),
      status: remaining > 0 ? 'partial' : 'completed'
    };

    setSales([newSale, ...sales]);
    
    // Reset form
    setCurrentSale([]);
    setCustomerName('');
    setDiscount('0');
    setTax('0');
    setAmountPaid('');
    setIsCreatingSale(false);

    toast({
      title: "تم بنجاح",
      description: `تم إنشاء الفاتورة بنجاح ${remaining > 0 ? 'مع مبلغ متبقي' : ''}`,
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-600">مكتملة</Badge>;
      case 'partial':
        return <Badge className="bg-yellow-600">مدفوعة جزئياً</Badge>;
      default:
        return <Badge variant="secondary">معلقة</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <ShoppingCart className="w-5 h-5" />
            إدارة المبيعات
          </CardTitle>
          <CardDescription className="text-blue-200">
            إنشاء فواتير جديدة وإدارة المبيعات
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Dialog open={isCreatingSale} onOpenChange={setIsCreatingSale}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="w-4 h-4 ml-2" />
                فاتورة جديدة
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl bg-slate-800 border-slate-600 text-white">
              <DialogHeader>
                <DialogTitle>إنشاء فاتورة جديدة</DialogTitle>
                <DialogDescription className="text-blue-200">
                  قم بإضافة المنتجات وإدخال بيانات العميل
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6">
                {/* Customer Info */}
                <div>
                  <Label htmlFor="customer-name" className="text-white">اسم العميل (اختياري)</Label>
                  <Input
                    id="customer-name"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="اسم العميل"
                  />
                </div>

                {/* Add Products */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">إضافة منتجات</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="product-select" className="text-white">المنتج</Label>
                      <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                          <SelectValue placeholder="اختر منتج" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          {mockProducts.map(product => {
                            console.log('SalesManager - Product SelectItem:', { productId: product.id, productName: product.name });
                            return (
                              <SelectItem key={product.id} value={product.id}>
                                {product.name} - {product.sellPrice.toFixed(2)} ج.م
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="quantity-input" className="text-white">الكمية</Label>
                      <Input
                        id="quantity-input"
                        type="number"
                        min="1"
                        value={quantity}
                        onChange={(e) => setQuantity(e.target.value)}
                        className="bg-slate-700 border-slate-600 text-white"
                      />
                    </div>
                    <div className="flex items-end">
                      <Button onClick={addProductToSale} className="w-full bg-green-600 hover:bg-green-700">
                        <Plus className="w-4 h-4 ml-2" />
                        إضافة
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Current Sale Items */}
                {currentSale.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">المنتجات المضافة</h3>
                    <div className="space-y-2">
                      {currentSale.map(item => (
                        <div key={item.productId} className="flex justify-between items-center p-3 bg-slate-700 rounded-lg">
                          <div className="flex-1">
                            <span className="text-white font-medium">{item.productName}</span>
                            <div className="text-sm text-gray-300">
                              {item.quantity} × {item.unitPrice.toFixed(2)} ج.م
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <span className="text-white font-semibold currency">
                              {item.total.toFixed(2)} ج.م
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                              onClick={() => removeProductFromSale(item.productId)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Calculation */}
                {currentSale.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">حساب الفاتورة</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="discount" className="text-white">نسبة الخصم (%)</Label>
                        <Input
                          id="discount"
                          type="number"
                          min="0"
                          max="100"
                          value={discount}
                          onChange={(e) => setDiscount(e.target.value)}
                          className="bg-slate-700 border-slate-600 text-white"
                        />
                      </div>
                      <div>
                        <Label htmlFor="tax" className="text-white">نسبة الضريبة (%)</Label>
                        <Input
                          id="tax"
                          type="number"
                          min="0"
                          value={tax}
                          onChange={(e) => setTax(e.target.value)}
                          className="bg-slate-700 border-slate-600 text-white"
                        />
                      </div>
                    </div>
                    
                    <div className="bg-slate-700 p-4 rounded-lg space-y-2">
                      <div className="flex justify-between text-white">
                        <span>المجموع الفرعي:</span>
                        <span className="currency">{calculateSubtotal().toFixed(2)} ج.م</span>
                      </div>
                      {parseFloat(discount) > 0 && (
                        <div className="flex justify-between text-green-400">
                          <span>الخصم ({discount}%):</span>
                          <span className="currency">-{((calculateSubtotal() * parseFloat(discount)) / 100).toFixed(2)} ج.م</span>
                        </div>
                      )}
                      {parseFloat(tax) > 0 && (
                        <div className="flex justify-between text-red-400">
                          <span>الضريبة ({tax}%):</span>
                          <span className="currency">+{(((calculateSubtotal() - (calculateSubtotal() * parseFloat(discount || '0')) / 100) * parseFloat(tax)) / 100).toFixed(2)} ج.م</span>
                        </div>
                      )}
                      <div className="flex justify-between text-xl font-bold text-white border-t border-slate-600 pt-2">
                        <span>الإجمالي:</span>
                        <span className="currency">{calculateTotal().toFixed(2)} ج.م</span>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="amount-paid" className="text-white">المبلغ المدفوع</Label>
                      <Input
                        id="amount-paid"
                        type="number"
                        min="0"
                        value={amountPaid}
                        onChange={(e) => setAmountPaid(e.target.value)}
                        className="bg-slate-700 border-slate-600 text-white"
                        placeholder="0.00"
                      />
                      {amountPaid && parseFloat(amountPaid) < calculateTotal() && (
                        <p className="text-yellow-400 text-sm mt-1">
                          المبلغ المتبقي: {(calculateTotal() - parseFloat(amountPaid)).toFixed(2)} ج.م
                        </p>
                      )}
                    </div>

                    <Button onClick={completeSale} className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-3">
                      <Calculator className="w-5 h-5 ml-2" />
                      إنهاء الفاتورة
                    </Button>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>

      {/* Sales History */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white">سجل المبيعات</CardTitle>
          <CardDescription className="text-blue-200">
            جميع الفواتير المسجلة في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          {sales.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">لا توجد فواتير حتى الآن</p>
              <p className="text-gray-500">قم بإنشاء فاتورة جديدة لبدء التسجيل</p>
            </div>
          ) : (
            <div className="space-y-4">
              {sales.map(sale => (
                <Card key={sale.id} className="border-slate-600 bg-slate-700/50">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-white font-semibold">فاتورة #{sale.id.slice(-6)}</h3>
                        <p className="text-gray-300">العميل: {sale.customerName}</p>
                        <p className="text-gray-400 text-sm">
                          {sale.createdAt.toLocaleDateString('ar-EG')} - {sale.createdAt.toLocaleTimeString('ar-EG')}
                        </p>
                      </div>
                      <div className="text-left">
                        {getStatusBadge(sale.status)}
                        <p className="text-white font-bold text-lg currency mt-2">
                          {sale.total.toFixed(2)} ج.م
                        </p>
                        {sale.remainingAmount > 0 && (
                          <p className="text-red-400 text-sm">
                            متبقي: {sale.remainingAmount.toFixed(2)} ج.م
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="text-sm text-gray-300">
                      <p>عدد المنتجات: {sale.items.length}</p>
                      <p>المبلغ المدفوع: {sale.amountPaid.toFixed(2)} ج.م</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SalesManager;
