import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Settings, Database, Users, Download, Upload } from 'lucide-react';

const SimpleSettingsManager = () => {
  const [settings, setSettings] = useState({
    storeName: 'محل المنظفات الذهبي',
    storeAddress: 'شارع النصر، المنصورة، مصر',
    storePhone: '01234567890',
    defaultTax: '14',
    defaultDiscount: '0'
  });

  const [activeTab, setActiveTab] = useState('general');

  const handleSettingChange = (key: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = () => {
    localStorage.setItem('storeSettings', JSON.stringify(settings));
    alert('تم حفظ الإعدادات بنجاح');
  };

  const exportData = () => {
    const data = {
      exportDate: new Date(),
      settings: settings,
      note: 'نسخة احتياطية من بيانات المحل'
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    
    alert('تم تصدير النسخة الاحتياطية بنجاح');
  };

  return (
    <div className="space-y-6 p-4">
      {/* Header */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Settings className="w-5 h-5" />
            إعدادات النظام
          </CardTitle>
          <CardDescription className="text-blue-200">
            تخصيص النظام وإدارة المستخدمين والنسخ الاحتياطية
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Tabs */}
      <div className="flex gap-2 mb-6">
        <Button
          onClick={() => setActiveTab('general')}
          className={`${activeTab === 'general' ? 'bg-blue-600' : 'bg-slate-700'} text-white`}
        >
          الإعدادات العامة
        </Button>
        <Button
          onClick={() => setActiveTab('users')}
          className={`${activeTab === 'users' ? 'bg-blue-600' : 'bg-slate-700'} text-white`}
        >
          المستخدمين
        </Button>
        <Button
          onClick={() => setActiveTab('backup')}
          className={`${activeTab === 'backup' ? 'bg-blue-600' : 'bg-slate-700'} text-white`}
        >
          النسخ الاحتياطية
        </Button>
      </div>

      {/* General Settings */}
      {activeTab === 'general' && (
        <Card className="border-slate-600 bg-slate-800/50">
          <CardHeader>
            <CardTitle className="text-white">معلومات المحل</CardTitle>
            <CardDescription className="text-blue-200">
              البيانات الأساسية للمحل
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-white">اسم المحل</Label>
              <Input
                value={settings.storeName}
                onChange={(e) => handleSettingChange('storeName', e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <div>
              <Label className="text-white">عنوان المحل</Label>
              <Input
                value={settings.storeAddress}
                onChange={(e) => handleSettingChange('storeAddress', e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <div>
              <Label className="text-white">رقم الهاتف</Label>
              <Input
                value={settings.storePhone}
                onChange={(e) => handleSettingChange('storePhone', e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-white">نسبة الضريبة الافتراضية (%)</Label>
                <Input
                  type="number"
                  value={settings.defaultTax}
                  onChange={(e) => handleSettingChange('defaultTax', e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
              <div>
                <Label className="text-white">نسبة الخصم الافتراضية (%)</Label>
                <Input
                  type="number"
                  value={settings.defaultDiscount}
                  onChange={(e) => handleSettingChange('defaultDiscount', e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>

            <Button onClick={saveSettings} className="bg-blue-600 hover:bg-blue-700">
              حفظ الإعدادات
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Users Management */}
      {activeTab === 'users' && (
        <Card className="border-slate-600 bg-slate-800/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Users className="w-5 h-5" />
              إدارة المستخدمين
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Card className="border-slate-700 bg-slate-700/50">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-white font-medium">المدير العام</p>
                      <p className="text-gray-300 text-sm">admin</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">مدير عام</span>
                      <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">نشط</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Backup */}
      {activeTab === 'backup' && (
        <Card className="border-slate-600 bg-slate-800/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Database className="w-5 h-5" />
              النسخ الاحتياطية
            </CardTitle>
            <CardDescription className="text-blue-200">
              تصدير واستيراد البيانات للحفاظ على أمان المعلومات
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-white font-semibold mb-4">تصدير البيانات</h3>
              <p className="text-gray-300 mb-4">
                قم بتصدير جميع بيانات المحل في ملف احتياطي
              </p>
              <Button onClick={exportData} className="bg-blue-600 hover:bg-blue-700">
                <Download className="w-4 h-4 ml-2" />
                تصدير نسخة احتياطية
              </Button>
            </div>

            <div className="bg-yellow-600/10 border border-yellow-600/30 rounded-lg p-4">
              <h4 className="text-yellow-400 font-semibold mb-2">تنبيه مهم</h4>
              <p className="text-yellow-200 text-sm">
                يُنصح بعمل نسخة احتياطية بشكل دوري لحماية البيانات من الفقدان. 
                احتفظ بالنسخ الاحتياطية في مكان آمن.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SimpleSettingsManager;
