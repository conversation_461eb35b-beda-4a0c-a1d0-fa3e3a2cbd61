
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Users, Plus, Search, Edit, Trash2, Phone, MapPin, CreditCard } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Customer {
  id: string;
  name: string;
  phone: string;
  address?: string;
  totalPurchases: number;
  totalDebt: number;
  createdAt: Date;
  lastPurchase?: Date;
}

interface Debt {
  id: string;
  customerId: string;
  customerName: string;
  amount: number;
  amountPaid: number;
  remainingAmount: number;
  description: string;
  createdAt: Date;
  status: 'pending' | 'partial' | 'paid';
}

const CustomersManager = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [debts, setDebts] = useState<Debt[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddingCustomer, setIsAddingCustomer] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [activeTab, setActiveTab] = useState('customers');
  const { toast } = useToast();

  // إضافة بيانات تجريبية للاختبار
  React.useEffect(() => {
    if (customers.length === 0) {
      const sampleCustomers: Customer[] = [
        {
          id: '1',
          name: 'أحمد محمد',
          phone: '01234567890',
          address: 'شارع النصر، المنصورة',
          totalPurchases: 1500,
          totalDebt: 250,
          createdAt: new Date('2024-01-15'),
          lastPurchase: new Date('2024-01-20')
        },
        {
          id: '2',
          name: 'فاطمة علي',
          phone: '01098765432',
          address: 'شارع الجمهورية، القاهرة',
          totalPurchases: 2300,
          totalDebt: 0,
          createdAt: new Date('2024-01-10'),
          lastPurchase: new Date('2024-01-25')
        }
      ];
      setCustomers(sampleCustomers);
    }
  }, [customers.length]);

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    address: ''
  });

  const [debtFormData, setDebtFormData] = useState({
    customerId: '',
    amount: '',
    amountPaid: '',
    description: ''
  });

  const resetForm = () => {
    setFormData({ name: '', phone: '', address: '' });
  };

  const resetDebtForm = () => {
    setDebtFormData({ customerId: '', amount: '', amountPaid: '', description: '' });
  };

  const handleAddCustomer = () => {
    if (!formData.name || !formData.phone) {
      toast({
        title: "خطأ",
        description: "يرجى ملء الاسم ورقم الهاتف على الأقل",
        variant: "destructive"
      });
      return;
    }

    const newCustomer: Customer = {
      id: Date.now().toString(),
      name: formData.name,
      phone: formData.phone,
      address: formData.address,
      totalPurchases: 0,
      totalDebt: 0,
      createdAt: new Date()
    };

    setCustomers([...customers, newCustomer]);
    resetForm();
    setIsAddingCustomer(false);
    
    toast({
      title: "تم بنجاح",
      description: "تم إضافة العميل بنجاح",
    });
  };

  const handleEditCustomer = () => {
    if (!editingCustomer) return;

    const updatedCustomers = customers.map(customer =>
      customer.id === editingCustomer.id
        ? {
            ...customer,
            name: formData.name,
            phone: formData.phone,
            address: formData.address
          }
        : customer
    );

    setCustomers(updatedCustomers);
    setEditingCustomer(null);
    resetForm();
    
    toast({
      title: "تم بنجاح",
      description: "تم تحديث بيانات العميل بنجاح",
    });
  };

  const handleDeleteCustomer = (customerId: string) => {
    setCustomers(customers.filter(customer => customer.id !== customerId));
    // حذف جميع الديون المرتبطة بالعميل
    setDebts(debts.filter(debt => debt.customerId !== customerId));
    
    toast({
      title: "تم بنجاح",
      description: "تم حذف العميل وجميع ديونه بنجاح",
    });
  };

  const handleAddDebt = () => {
    if (!debtFormData.customerId || !debtFormData.amount || !debtFormData.description) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    const customer = customers.find(c => c.id === debtFormData.customerId);
    if (!customer) return;

    const amount = parseFloat(debtFormData.amount);
    const amountPaid = parseFloat(debtFormData.amountPaid || '0');
    const remainingAmount = amount - amountPaid;

    const newDebt: Debt = {
      id: Date.now().toString(),
      customerId: debtFormData.customerId,
      customerName: customer.name,
      amount,
      amountPaid,
      remainingAmount,
      description: debtFormData.description,
      createdAt: new Date(),
      status: remainingAmount > 0 ? (amountPaid > 0 ? 'partial' : 'pending') : 'paid'
    };

    setDebts([...debts, newDebt]);
    
    // تحديث إجمالي ديون العميل
    const updatedCustomers = customers.map(customer =>
      customer.id === debtFormData.customerId
        ? { ...customer, totalDebt: customer.totalDebt + remainingAmount }
        : customer
    );
    setCustomers(updatedCustomers);

    resetDebtForm();
    
    toast({
      title: "تم بنجاح",
      description: "تم تسجيل الدين بنجاح",
    });
  };

  const handlePayDebt = (debtId: string, paymentAmount: number) => {
    const debt = debts.find(d => d.id === debtId);
    if (!debt) return;

    const newAmountPaid = debt.amountPaid + paymentAmount;
    const newRemainingAmount = debt.amount - newAmountPaid;

    const updatedDebts = debts.map(d =>
      d.id === debtId
        ? {
            ...d,
            amountPaid: newAmountPaid,
            remainingAmount: newRemainingAmount,
            status: newRemainingAmount > 0 ? 'partial' : 'paid'
          }
        : d
    );

    setDebts(updatedDebts);

    // تحديث إجمالي ديون العميل
    const updatedCustomers = customers.map(customer =>
      customer.id === debt.customerId
        ? { ...customer, totalDebt: customer.totalDebt - paymentAmount }
        : customer
    );
    setCustomers(updatedCustomers);

    toast({
      title: "تم بنجاح",
      description: `تم تسجيل دفعة بقيمة ${paymentAmount.toFixed(2)} ج.م`,
    });
  };

  const openEditDialog = (customer: Customer) => {
    setEditingCustomer(customer);
    setFormData({
      name: customer.name,
      phone: customer.phone,
      address: customer.address || ''
    });
  };

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone.includes(searchTerm)
  );

  const pendingDebts = debts.filter(debt => debt.status !== 'paid');
  const totalPendingDebt = pendingDebts.reduce((sum, debt) => sum + debt.remainingAmount, 0);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-600">مدفوع</Badge>;
      case 'partial':
        return <Badge className="bg-yellow-600">مدفوع جزئياً</Badge>;
      default:
        return <Badge variant="destructive">معلق</Badge>;
    }
  };

  return (
    <div className="space-y-6 min-h-screen">
      {/* Header */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Users className="w-5 h-5" />
            إدارة العملاء والمديونيات
          </CardTitle>
          <CardDescription className="text-blue-200">
            إدارة بيانات العملاء ومتابعة المديونيات
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <Card className="border-blue-600/30 bg-blue-600/10">
              <CardContent className="p-4 text-center">
                <Users className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">{customers.length}</p>
                <p className="text-blue-200 text-sm">إجمالي العملاء</p>
              </CardContent>
            </Card>
            <Card className="border-yellow-600/30 bg-yellow-600/10">
              <CardContent className="p-4 text-center">
                <CreditCard className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">{pendingDebts.length}</p>
                <p className="text-yellow-200 text-sm">ديون معلقة</p>
              </CardContent>
            </Card>
            <Card className="border-red-600/30 bg-red-600/10">
              <CardContent className="p-4 text-center">
                <CreditCard className="w-8 h-8 text-red-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white currency">{totalPendingDebt.toFixed(2)} ج.م</p>
                <p className="text-red-200 text-sm">إجمالي الديون المعلقة</p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-slate-800">
          <TabsTrigger value="customers">العملاء</TabsTrigger>
          <TabsTrigger value="debts">المديونيات</TabsTrigger>
        </TabsList>

        <TabsContent value="customers" className="space-y-4">
          {/* Search and Add Customer */}
          <Card className="border-slate-600 bg-slate-800/50">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="البحث بالاسم أو رقم الهاتف..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pr-10 bg-slate-700 border-slate-600 text-white"
                    />
                  </div>
                </div>
                
                <Dialog open={isAddingCustomer} onOpenChange={setIsAddingCustomer}>
                  <DialogTrigger asChild>
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                      <Plus className="w-4 h-4 ml-2" />
                      إضافة عميل جديد
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="bg-slate-800 border-slate-600 text-white">
                    <DialogHeader>
                      <DialogTitle>إضافة عميل جديد</DialogTitle>
                      <DialogDescription className="text-blue-200">
                        قم بإدخال بيانات العميل الجديد
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="customer-name" className="text-white">اسم العميل *</Label>
                        <Input
                          id="customer-name"
                          value={formData.name}
                          onChange={(e) => setFormData({...formData, name: e.target.value})}
                          className="bg-slate-700 border-slate-600 text-white"
                          placeholder="أحمد محمد"
                        />
                      </div>
                      <div>
                        <Label htmlFor="customer-phone" className="text-white">رقم الهاتف *</Label>
                        <Input
                          id="customer-phone"
                          value={formData.phone}
                          onChange={(e) => setFormData({...formData, phone: e.target.value})}
                          className="bg-slate-700 border-slate-600 text-white"
                          placeholder="01234567890"
                        />
                      </div>
                      <div>
                        <Label htmlFor="customer-address" className="text-white">العنوان (اختياري)</Label>
                        <Input
                          id="customer-address"
                          value={formData.address}
                          onChange={(e) => setFormData({...formData, address: e.target.value})}
                          className="bg-slate-700 border-slate-600 text-white"
                          placeholder="شارع النصر، المنصورة"
                        />
                      </div>
                      <Button onClick={handleAddCustomer} className="w-full bg-blue-600 hover:bg-blue-700">
                        إضافة العميل
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>

          {/* Customers List */}
          <div className="grid gap-4">
            {filteredCustomers.length === 0 ? (
              <Card className="border-slate-600 bg-slate-800/50">
                <CardContent className="p-8 text-center">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">لا توجد عملاء حتى الآن</p>
                  <p className="text-gray-500">قم بإضافة عميل جديد لبدء الإدارة</p>
                </CardContent>
              </Card>
            ) : (
              filteredCustomers.map(customer => (
                <Card key={customer.id} className="border-slate-600 bg-slate-800/50 hover:bg-slate-800/70 transition-colors">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-white mb-2">{customer.name}</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <Phone className="w-4 h-4 text-blue-400" />
                            <span className="text-white">{customer.phone}</span>
                          </div>
                          {customer.address && (
                            <div className="flex items-center gap-2">
                              <MapPin className="w-4 h-4 text-blue-400" />
                              <span className="text-gray-300">{customer.address}</span>
                            </div>
                          )}
                          <div>
                            <span className="text-gray-400">إجمالي الديون:</span>
                            <span className={`font-medium currency ${customer.totalDebt > 0 ? 'text-red-400' : 'text-green-400'}`}>
                              {customer.totalDebt.toFixed(2)} ج.م
                            </span>
                          </div>
                        </div>
                        <p className="text-gray-400 text-sm mt-2">
                          تاريخ الإضافة: {customer.createdAt.toLocaleDateString('ar-EG')}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white"
                              onClick={() => openEditDialog(customer)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="bg-slate-800 border-slate-600 text-white">
                            <DialogHeader>
                              <DialogTitle>تعديل بيانات العميل</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <Label htmlFor="edit-customer-name" className="text-white">اسم العميل *</Label>
                                <Input
                                  id="edit-customer-name"
                                  value={formData.name}
                                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                                  className="bg-slate-700 border-slate-600 text-white"
                                />
                              </div>
                              <div>
                                <Label htmlFor="edit-customer-phone" className="text-white">رقم الهاتف *</Label>
                                <Input
                                  id="edit-customer-phone"
                                  value={formData.phone}
                                  onChange={(e) => setFormData({...formData, phone: e.target.value})}
                                  className="bg-slate-700 border-slate-600 text-white"
                                />
                              </div>
                              <div>
                                <Label htmlFor="edit-customer-address" className="text-white">العنوان</Label>
                                <Input
                                  id="edit-customer-address"
                                  value={formData.address}
                                  onChange={(e) => setFormData({...formData, address: e.target.value})}
                                  className="bg-slate-700 border-slate-600 text-white"
                                />
                              </div>
                              <Button onClick={handleEditCustomer} className="w-full bg-blue-600 hover:bg-blue-700">
                                حفظ التعديل
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                          onClick={() => handleDeleteCustomer(customer.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="debts" className="space-y-4">
          {/* Add Debt */}
          <Card className="border-slate-600 bg-slate-800/50">
            <CardHeader>
              <CardTitle className="text-white">إضافة دين جديد</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="debt-customer" className="text-white">العميل *</Label>
                  <select
                    id="debt-customer"
                    value={debtFormData.customerId}
                    onChange={(e) => setDebtFormData({...debtFormData, customerId: e.target.value})}
                    className="w-full p-2 bg-slate-700 border border-slate-600 rounded-md text-white"
                  >
                    <option value="">اختر العميل</option>
                    {customers.map(customer => (
                      <option key={customer.id} value={customer.id}>{customer.name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <Label htmlFor="debt-amount" className="text-white">المبلغ الإجمالي (جنيه) *</Label>
                  <Input
                    id="debt-amount"
                    type="number"
                    value={debtFormData.amount}
                    onChange={(e) => setDebtFormData({...debtFormData, amount: e.target.value})}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="debt-paid" className="text-white">المبلغ المدفوع (جنيه)</Label>
                  <Input
                    id="debt-paid"
                    type="number"
                    value={debtFormData.amountPaid}
                    onChange={(e) => setDebtFormData({...debtFormData, amountPaid: e.target.value})}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="debt-description" className="text-white">وصف المعاملة *</Label>
                  <Input
                    id="debt-description"
                    value={debtFormData.description}
                    onChange={(e) => setDebtFormData({...debtFormData, description: e.target.value})}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="فاتورة شراء منتجات تنظيف"
                  />
                </div>
              </div>
              <Button onClick={handleAddDebt} className="mt-4 bg-blue-600 hover:bg-blue-700">
                تسجيل الدين
              </Button>
            </CardContent>
          </Card>

          {/* Debts List */}
          <Card className="border-slate-600 bg-slate-800/50">
            <CardHeader>
              <CardTitle className="text-white">قائمة المديونيات</CardTitle>
            </CardHeader>
            <CardContent>
              {debts.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">لا توجد ديون مسجلة</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {debts.map(debt => (
                    <Card key={debt.id} className="border-slate-700 bg-slate-700/50">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-white font-semibold">{debt.customerName}</h3>
                              {getStatusBadge(debt.status)}
                            </div>
                            <p className="text-gray-300 mb-2">{debt.description}</p>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <span className="text-gray-400">المبلغ الإجمالي:</span>
                                <p className="text-white font-medium currency">{debt.amount.toFixed(2)} ج.م</p>
                              </div>
                              <div>
                                <span className="text-gray-400">المبلغ المدفوع:</span>
                                <p className="text-green-400 font-medium currency">{debt.amountPaid.toFixed(2)} ج.م</p>
                              </div>
                              <div>
                                <span className="text-gray-400">المبلغ المتبقي:</span>
                                <p className="text-red-400 font-medium currency">{debt.remainingAmount.toFixed(2)} ج.م</p>
                              </div>
                            </div>
                            <p className="text-gray-400 text-sm mt-2">
                              تاريخ التسجيل: {debt.createdAt.toLocaleDateString('ar-EG')}
                            </p>
                          </div>
                          {debt.remainingAmount > 0 && (
                            <div className="flex gap-2">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="border-green-600 text-green-400 hover:bg-green-600 hover:text-white"
                                  >
                                    تسجيل دفعة
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="bg-slate-800 border-slate-600 text-white">
                                  <DialogHeader>
                                    <DialogTitle>تسجيل دفعة جديدة</DialogTitle>
                                    <DialogDescription className="text-blue-200">
                                      العميل: {debt.customerName} - المتبقي: {debt.remainingAmount.toFixed(2)} ج.م
                                    </DialogDescription>
                                  </DialogHeader>
                                  <div>
                                    <Label htmlFor="payment-amount" className="text-white">مبلغ الدفعة (جنيه)</Label>
                                    <Input
                                      id="payment-amount"
                                      type="number"
                                      max={debt.remainingAmount}
                                      className="bg-slate-700 border-slate-600 text-white"
                                      placeholder="0.00"
                                      onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                          const amount = parseFloat((e.target as HTMLInputElement).value);
                                          if (amount > 0 && amount <= debt.remainingAmount) {
                                            handlePayDebt(debt.id, amount);
                                            (e.target as HTMLInputElement).value = '';
                                          }
                                        }
                                      }}
                                    />
                                  </div>
                                </DialogContent>
                              </Dialog>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CustomersManager;
