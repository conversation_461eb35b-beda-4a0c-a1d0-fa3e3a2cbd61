import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Search, Edit, Trash2, Package } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Product {
  id: string;
  name: string;
  category: string;
  costPrice: number;
  sellPrice: number;
  quantity: number;
  createdAt: Date;
}

const categories = [
  'مطهرات',
  'منظفات ملابس',
  'منظفات أطباق',
  'معطرات',
  'منظفات أرضيات',
  'منظفات حمامات',
  'أخرى'
];

const ProductsManager = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isAddingProduct, setIsAddingProduct] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const { toast } = useToast();

  // إضافة بيانات تجريبية للاختبار
  React.useEffect(() => {
    if (products.length === 0) {
      const sampleProducts: Product[] = [
        {
          id: '1',
          name: 'صابون أومو',
          category: 'منظفات ملابس',
          costPrice: 20.00,
          sellPrice: 25.50,
          quantity: 100,
          createdAt: new Date('2024-01-15')
        },
        {
          id: '2',
          name: 'فيري منظف أطباق',
          category: 'منظفات أطباق',
          costPrice: 12.00,
          sellPrice: 15.75,
          quantity: 50,
          createdAt: new Date('2024-01-10')
        },
        {
          id: '3',
          name: 'كلوركس مطهر',
          category: 'مطهرات',
          costPrice: 8.50,
          sellPrice: 12.00,
          quantity: 75,
          createdAt: new Date('2024-01-12')
        }
      ];
      setProducts(sampleProducts);
    }
  }, [products.length]);

  const [formData, setFormData] = useState({
    name: '',
    category: '',
    costPrice: '',
    sellPrice: '',
    quantity: ''
  });

  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      costPrice: '',
      sellPrice: '',
      quantity: ''
    });
  };

  const handleAddProduct = () => {
    if (!formData.name || !formData.category || !formData.costPrice || !formData.sellPrice || !formData.quantity) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    const newProduct: Product = {
      id: Date.now().toString(),
      name: formData.name,
      category: formData.category,
      costPrice: parseFloat(formData.costPrice),
      sellPrice: parseFloat(formData.sellPrice),
      quantity: parseInt(formData.quantity),
      createdAt: new Date()
    };

    setProducts([...products, newProduct]);
    resetForm();
    setIsAddingProduct(false);
    
    toast({
      title: "تم بنجاح",
      description: "تم إضافة المنتج بنجاح",
    });
  };

  const handleEditProduct = () => {
    if (!editingProduct) return;

    const updatedProducts = products.map(product =>
      product.id === editingProduct.id
        ? {
            ...product,
            name: formData.name,
            category: formData.category,
            costPrice: parseFloat(formData.costPrice),
            sellPrice: parseFloat(formData.sellPrice),
            quantity: parseInt(formData.quantity)
          }
        : product
    );

    setProducts(updatedProducts);
    setEditingProduct(null);
    resetForm();
    
    toast({
      title: "تم بنجاح",
      description: "تم تحديث المنتج بنجاح",
    });
  };

  const handleDeleteProduct = (productId: string) => {
    setProducts(products.filter(product => product.id !== productId));
    toast({
      title: "تم بنجاح",
      description: "تم حذف المنتج بنجاح",
    });
  };

  const openEditDialog = (product: Product) => {
    setEditingProduct(product);
    setFormData({
      name: product.name,
      category: product.category,
      costPrice: product.costPrice.toString(),
      sellPrice: product.sellPrice.toString(),
      quantity: product.quantity.toString()
    });
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const calculateProfit = (costPrice: number, sellPrice: number) => {
    return sellPrice - costPrice;
  };

  return (
    <div className="space-y-6">
      {/* Search and Filter */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Package className="w-5 h-5" />
            إدارة المنتجات
          </CardTitle>
          <CardDescription className="text-blue-200">
            إضافة وإدارة منتجات المحل
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1">
              <Label htmlFor="search" className="text-white mb-2 block">البحث عن منتج</Label>
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="search"
                  placeholder="ابحث عن منتج..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>
            <div className="flex-1">
              <Label htmlFor="category-filter" className="text-white mb-2 block">تصفية حسب التصنيف</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="جميع التصنيفات" />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">جميع التصنيفات</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Dialog open={isAddingProduct} onOpenChange={setIsAddingProduct}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="w-4 h-4 ml-2" />
                إضافة منتج جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-slate-800 border-slate-600 text-white">
              <DialogHeader>
                <DialogTitle>إضافة منتج جديد</DialogTitle>
                <DialogDescription className="text-blue-200">
                  قم بإدخال بيانات المنتج الجديد
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="product-name" className="text-white">اسم المنتج *</Label>
                  <Input
                    id="product-name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="مثال: صابون أومو"
                  />
                </div>
                <div>
                  <Label htmlFor="product-category" className="text-white">التصنيف *</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue placeholder="اختر التصنيف" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="cost-price" className="text-white">سعر التكلفة (جنيه) *</Label>
                    <Input
                      id="cost-price"
                      type="number"
                      value={formData.costPrice}
                      onChange={(e) => setFormData({...formData, costPrice: e.target.value})}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="0.00"
                    />
                  </div>
                  <div>
                    <Label htmlFor="sell-price" className="text-white">سعر البيع (جنيه) *</Label>
                    <Input
                      id="sell-price"
                      type="number"
                      value={formData.sellPrice}
                      onChange={(e) => setFormData({...formData, sellPrice: e.target.value})}
                      className="bg-slate-700 border-slate-600 text-white"
                      placeholder="0.00"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="quantity" className="text-white">الكمية المتوفرة *</Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={formData.quantity}
                    onChange={(e) => setFormData({...formData, quantity: e.target.value})}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="0"
                  />
                </div>
                <Button onClick={handleAddProduct} className="w-full bg-blue-600 hover:bg-blue-700">
                  إضافة المنتج
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>

      {/* Products List */}
      <div className="grid gap-4">
        {filteredProducts.length === 0 ? (
          <Card className="border-slate-600 bg-slate-800/50">
            <CardContent className="p-8 text-center">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">لا توجد منتجات حتى الآن</p>
              <p className="text-gray-500">قم بإضافة منتج جديد لبدء الإدارة</p>
            </CardContent>
          </Card>
        ) : (
          filteredProducts.map(product => (
            <Card key={product.id} className="border-slate-600 bg-slate-800/50 hover:bg-slate-800/70 transition-colors">
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-xl font-semibold text-white">{product.name}</h3>
                      <Badge variant="secondary" className="bg-blue-600/20 text-blue-300">
                        {product.category}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">سعر التكلفة:</span>
                        <p className="text-white font-medium currency">{product.costPrice.toFixed(2)} ج.م</p>
                      </div>
                      <div>
                        <span className="text-gray-400">سعر البيع:</span>
                        <p className="text-white font-medium currency">{product.sellPrice.toFixed(2)} ج.م</p>
                      </div>
                      <div>
                        <span className="text-gray-400">الربح المتوقع:</span>
                        <p className="text-green-400 font-medium currency">
                          {calculateProfit(product.costPrice, product.sellPrice).toFixed(2)} ج.م
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-400">الكمية المتوفرة:</span>
                        <p className={`font-medium ${product.quantity < 5 ? 'text-red-400' : 'text-white'}`}>
                          {product.quantity} وحدة
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-blue-600 text-blue-400 hover:bg-blue-600 hover:text-white"
                          onClick={() => openEditDialog(product)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="bg-slate-800 border-slate-600 text-white">
                        <DialogHeader>
                          <DialogTitle>تعديل المنتج</DialogTitle>
                          <DialogDescription className="text-blue-200">
                            قم بتعديل بيانات المنتج
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="edit-product-name" className="text-white">اسم المنتج *</Label>
                            <Input
                              id="edit-product-name"
                              value={formData.name}
                              onChange={(e) => setFormData({...formData, name: e.target.value})}
                              className="bg-slate-700 border-slate-600 text-white"
                            />
                          </div>
                          <div>
                            <Label htmlFor="edit-product-category" className="text-white">التصنيف *</Label>
                            <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent className="bg-slate-700 border-slate-600">
                                {categories.map(category => (
                                  <SelectItem key={category} value={category}>{category}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="edit-cost-price" className="text-white">سعر التكلفة (جنيه) *</Label>
                              <Input
                                id="edit-cost-price"
                                type="number"
                                value={formData.costPrice}
                                onChange={(e) => setFormData({...formData, costPrice: e.target.value})}
                                className="bg-slate-700 border-slate-600 text-white"
                              />
                            </div>
                            <div>
                              <Label htmlFor="edit-sell-price" className="text-white">سعر البيع (جنيه) *</Label>
                              <Input
                                id="edit-sell-price"
                                type="number"
                                value={formData.sellPrice}
                                onChange={(e) => setFormData({...formData, sellPrice: e.target.value})}
                                className="bg-slate-700 border-slate-600 text-white"
                              />
                            </div>
                          </div>
                          <div>
                            <Label htmlFor="edit-quantity" className="text-white">الكمية المتوفرة *</Label>
                            <Input
                              id="edit-quantity"
                              type="number"
                              value={formData.quantity}
                              onChange={(e) => setFormData({...formData, quantity: e.target.value})}
                              className="bg-slate-700 border-slate-600 text-white"
                            />
                          </div>
                          <Button onClick={handleEditProduct} className="w-full bg-blue-600 hover:bg-blue-700">
                            حفظ التعديل
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                      onClick={() => handleDeleteProduct(product.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default ProductsManager;
