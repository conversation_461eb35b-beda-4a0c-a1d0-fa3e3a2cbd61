import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Package, Plus, Edit, Trash2 } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  category: string;
  costPrice: number;
  sellPrice: number;
  quantity: number;
}

const SimpleProductsManager = () => {
  const [products, setProducts] = useState<Product[]>([
    {
      id: '1',
      name: 'صابون أومو',
      category: 'منظفات ملابس',
      costPrice: 20.00,
      sellPrice: 25.50,
      quantity: 100
    },
    {
      id: '2',
      name: 'فيري منظف أطباق',
      category: 'منظفات أطباق',
      costPrice: 12.00,
      sellPrice: 15.75,
      quantity: 50
    }
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [newProduct, setNewProduct] = useState({
    name: '',
    category: 'منظفات ملابس',
    costPrice: '',
    sellPrice: '',
    quantity: ''
  });

  const categories = [
    'منظفات ملابس',
    'منظفات أطباق',
    'مطهرات',
    'معطرات',
    'منظفات أرضيات'
  ];

  const handleAddProduct = () => {
    if (!newProduct.name || !newProduct.costPrice || !newProduct.sellPrice || !newProduct.quantity) {
      alert('يرجى ملء جميع الحقول');
      return;
    }

    const product: Product = {
      id: Date.now().toString(),
      name: newProduct.name,
      category: newProduct.category,
      costPrice: parseFloat(newProduct.costPrice),
      sellPrice: parseFloat(newProduct.sellPrice),
      quantity: parseInt(newProduct.quantity)
    };

    setProducts([...products, product]);
    setNewProduct({
      name: '',
      category: 'منظفات ملابس',
      costPrice: '',
      sellPrice: '',
      quantity: ''
    });
    setShowAddForm(false);
    alert('تم إضافة المنتج بنجاح');
  };

  const handleDeleteProduct = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      setProducts(products.filter(p => p.id !== id));
      alert('تم حذف المنتج');
    }
  };

  return (
    <div className="space-y-6 p-4">
      {/* Header */}
      <Card className="border-blue-600/50 bg-slate-800/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Package className="w-5 h-5" />
            إدارة المنتجات
          </CardTitle>
          <CardDescription className="text-blue-200">
            إضافة وإدارة منتجات المحل
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="w-4 h-4 ml-2" />
            {showAddForm ? 'إلغاء' : 'إضافة منتج جديد'}
          </Button>
        </CardContent>
      </Card>

      {/* Add Product Form */}
      {showAddForm && (
        <Card className="border-green-600/50 bg-slate-800/50">
          <CardHeader>
            <CardTitle className="text-white">إضافة منتج جديد</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-white">اسم المنتج</Label>
              <Input
                value={newProduct.name}
                onChange={(e) => setNewProduct({...newProduct, name: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="مثال: صابون أومو"
              />
            </div>
            
            <div>
              <Label className="text-white">التصنيف</Label>
              <select
                value={newProduct.category}
                onChange={(e) => setNewProduct({...newProduct, category: e.target.value})}
                className="w-full p-2 bg-slate-700 border border-slate-600 rounded-md text-white"
              >
                {categories.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-white">سعر التكلفة</Label>
                <Input
                  type="number"
                  value={newProduct.costPrice}
                  onChange={(e) => setNewProduct({...newProduct, costPrice: e.target.value})}
                  className="bg-slate-700 border-slate-600 text-white"
                  placeholder="0.00"
                />
              </div>
              <div>
                <Label className="text-white">سعر البيع</Label>
                <Input
                  type="number"
                  value={newProduct.sellPrice}
                  onChange={(e) => setNewProduct({...newProduct, sellPrice: e.target.value})}
                  className="bg-slate-700 border-slate-600 text-white"
                  placeholder="0.00"
                />
              </div>
            </div>

            <div>
              <Label className="text-white">الكمية</Label>
              <Input
                type="number"
                value={newProduct.quantity}
                onChange={(e) => setNewProduct({...newProduct, quantity: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="0"
              />
            </div>

            <Button 
              onClick={handleAddProduct}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              إضافة المنتج
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Products List */}
      <div className="grid gap-4">
        {products.map(product => (
          <Card key={product.id} className="border-slate-600 bg-slate-800/50">
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white mb-2">{product.name}</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">التصنيف:</span>
                      <p className="text-white">{product.category}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">سعر التكلفة:</span>
                      <p className="text-white">{product.costPrice.toFixed(2)} ج.م</p>
                    </div>
                    <div>
                      <span className="text-gray-400">سعر البيع:</span>
                      <p className="text-white">{product.sellPrice.toFixed(2)} ج.م</p>
                    </div>
                    <div>
                      <span className="text-gray-400">الكمية:</span>
                      <p className="text-white">{product.quantity} وحدة</p>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                    onClick={() => handleDeleteProduct(product.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default SimpleProductsManager;
