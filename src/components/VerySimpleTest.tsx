import React from 'react';

interface VerySimpleTestProps {
  tabName?: string;
}

const VerySimpleTest = ({ tabName = 'Unknown' }: VerySimpleTestProps) => {
  console.log('VerySimpleTest rendering for tab:', tabName);

  return (
    <div style={{
      backgroundColor: 'white',
      color: 'black',
      padding: '20px',
      margin: '20px',
      border: '5px solid green',
      fontSize: '18px'
    }}>
      <h1 style={{ color: 'green' }}>✅ TEST COMPONENT WORKING!</h1>
      <p style={{ color: 'blue' }}>Tab: {tabName}</p>
      <p style={{ color: 'red' }}>If you see this, the tab is working!</p>
      <p>مكون الاختبار يعمل!</p>
      <p>Time: {new Date().toLocaleTimeString()}</p>
    </div>
  );
};

export default VerySimpleTest;
