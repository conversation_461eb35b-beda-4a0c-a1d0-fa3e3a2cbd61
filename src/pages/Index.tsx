
import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ProductsManager from '@/components/ProductsManager';
import SalesManager from '@/components/SalesManager';
import CustomersManager from '@/components/CustomersManager';
import ReportsManager from '@/components/ReportsManager';
import SettingsManager from '@/components/SettingsManager';
import TestComponent from '@/components/TestComponent';
import SimpleTest from '@/components/SimpleTest';
import VerySimpleTest from '@/components/VerySimpleTest';
import { Package, ShoppingCart, Users, FileText, Settings } from 'lucide-react';

const Index = () => {
  const [activeTab, setActiveTab] = useState('products');

  console.log('Index component rendering, activeTab:', activeTab);

  const handleTabChange = (value: string) => {
    console.log('Tab changing to:', value);
    setActiveTab(value);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <Card className="mb-6 border-blue-600/50 bg-gradient-to-r from-slate-800 to-blue-800">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold text-white mb-2">
              🧽 نظام إدارة محل المنظفات
            </CardTitle>
            <CardDescription className="text-blue-200 text-lg">
              نظام إدارة شامل لمحلات المنظفات والمطهرات - العملة: الجنيه المصري
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full" dir="rtl">
          <TabsList className="grid w-full grid-cols-5 bg-slate-800 p-2 rounded-lg border border-blue-600/30">
            <TabsTrigger 
              value="products" 
              className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <Package className="w-4 h-4" />
              المنتجات
            </TabsTrigger>
            <TabsTrigger 
              value="sales" 
              className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <ShoppingCart className="w-4 h-4" />
              المبيعات
            </TabsTrigger>
            <TabsTrigger 
              value="customers" 
              className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <Users className="w-4 h-4" />
              العملاء
            </TabsTrigger>
            <TabsTrigger 
              value="reports" 
              className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <FileText className="w-4 h-4" />
              التقارير
            </TabsTrigger>
            <TabsTrigger 
              value="settings" 
              className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <Settings className="w-4 h-4" />
              الإعدادات
            </TabsTrigger>
          </TabsList>

          <TabsContent value="products" className="mt-4">
            <ProductsManager />
          </TabsContent>

          <TabsContent value="sales" className="mt-4">
            <SalesManager />
          </TabsContent>

          <TabsContent value="customers" className="mt-4">
            <CustomersManager />
          </TabsContent>

          <TabsContent value="reports" className="mt-4">
            <ReportsManager />
          </TabsContent>

          <TabsContent value="settings" className="mt-4">
            <SettingsManager />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Index;
